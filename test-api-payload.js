// Test script to verify API payload includes TTS voice type
// This simulates what the frontend sends to the API

const testApiPayload = () => {
  // Simulate the srtOptions object that gets sent to the API
  const srtOptions = {
    embedOriginalSubtitles: true,
    embedTranslatedSubtitles: true,
    subtitleStyle: {
      fontFamily: "Arial",
      fontSize: 18,
      fontColor: "white",
      backgroundColor: "rgba(0,0,0,0.8)",
      position: "bottom",
      outline: true,
      outlineColor: "black"
    },
    outputFormat: "mp4",
    ttsService: "geminiConceptualTTS",
    ttsLanguageTarget: "vi",
    ttsLanguageVoice: "Puck",
    sourceLanguage: "Vietnamese (vi)",
    targetLanguage: "Thai (th)",
    apiKey: "your-api-key-here"
  };

  console.log('API Payload Test - srtOptions:');
  console.log(JSON.stringify(srtOptions, null, 2));

  // Verify all TTS fields are present
  const requiredTtsFields = ['ttsService', 'ttsLanguageTarget', 'ttsLanguageVoice', 'apiKey'];
  const missingFields = requiredTtsFields.filter(field => !srtOptions[field]);

  if (missingFields.length === 0) {
    console.log('✅ All TTS fields are present in the API payload');
  } else {
    console.log('❌ Missing TTS fields:', missingFields);
  }

  // Test with different voice types
  const testVoices = ['Puck', 'Zephyr', 'Charon', 'Kore'];
  console.log('\nTesting different voice types:');

  testVoices.forEach(voice => {
    const testPayload = { ...srtOptions, ttsLanguageVoice: voice };
    console.log(`Voice: ${voice} -> ttsLanguageVoice: "${testPayload.ttsLanguageVoice}"`);
  });
};

// Run the test
testApiPayload();

// Test script to verify API payload includes TTS voice type
// This simulates what the frontend sends to the API

const testApiPayload = () => {
  // Simulate the srtOptions object that gets sent to the API
  const srtOptions = {
    embedOriginalSubtitles: true,
    embedTranslatedSubtitles: true,
    subtitleStyle: {
      fontFamily: "Arial",
      fontSize: 18,
      fontColor: "white",
      backgroundColor: "rgba(0,0,0,0.8)",
      position: "bottom",
      outline: true,
      outlineColor: "black"
    },
    outputFormat: "mp4",
    ttsService: "geminiConceptualTTS",
    ttsLanguageTarget: "vi",
    ttsLanguageVoice: "Puck",
    sourceLanguage: "Vietnamese (vi)",
    targetLanguage: "Thai (th)",
    apiKey: "your-api-key-here"
  };

  console.log('API Payload Test - srtOptions:');
  console.log(JSON.stringify(srtOptions, null, 2));

  // Test speechSrtFile generation logic
  console.log('\nSpeechSrtFile Generation Test:');
  const testSubtitles = [
    { id: '1', startTime: '00:00:01,000', endTime: '00:00:03,000', originalText: 'Hello world', translatedText: 'Xin chào thế giới' },
    { id: '2', startTime: '00:00:04,000', endTime: '00:00:06,000', originalText: 'How are you?', translatedText: 'Bạn khỏe không?' }
  ];

  // Simulate speech SRT generation for source language (English)
  if (srtOptions.ttsLanguageTarget === 'en') {
    const speechSrtForSource = testSubtitles.map(sub => ({
      id: sub.id,
      startTime: sub.startTime,
      endTime: sub.endTime,
      text: sub.originalText
    }));
    console.log('Speech SRT for source language (English):', speechSrtForSource);
  }

  // Simulate speech SRT generation for target language (Vietnamese)
  if (srtOptions.ttsLanguageTarget === 'vi') {
    const speechSrtForTarget = testSubtitles.map(sub => ({
      id: sub.id,
      startTime: sub.startTime,
      endTime: sub.endTime,
      text: sub.translatedText
    }));
    console.log('Speech SRT for target language (Vietnamese):', speechSrtForTarget);
  }

  // Verify all TTS fields are present
  const requiredTtsFields = ['ttsService', 'ttsLanguageTarget', 'ttsLanguageVoice', 'apiKey'];
  const missingFields = requiredTtsFields.filter(field => !srtOptions[field]);

  if (missingFields.length === 0) {
    console.log('✅ All TTS fields are present in the API payload');
  } else {
    console.log('❌ Missing TTS fields:', missingFields);
  }

  // Test with different voice types
  const testVoices = ['Puck', 'Zephyr', 'Charon', 'Kore'];
  console.log('\nTesting different voice types:');

  testVoices.forEach(voice => {
    const testPayload = { ...srtOptions, ttsLanguageVoice: voice };
    console.log(`Voice: ${voice} -> ttsLanguageVoice: "${testPayload.ttsLanguageVoice}"`);
  });
};

// Run the test
testApiPayload();


import React, { useState, useCallback, ChangeEvent, useEffect, useRef } from 'react';
import { MergedSubtitle, VideoProcessingOptions, VideoProcessingProgress, ProcessedVideoResult, SubtitleStyle } from './types';
import { translateExistingSubtitles } from './services/geminiService';
import { generateSubtitlesWithChatCompletion } from './services/chatCompletionService';
import { generateSRTContent, downloadSRTFile, formatSecondsToSRTTime, generateVTTContent, formatSRTTimeToVTTTime, downloadVideoFile, formatFileSize, getDefaultSubtitleStyle } from './utils/subtitleUtils';
import { UploadIcon, ProcessIcon, EditIcon, DeleteIcon, AddIcon, DownloadIcon, InfoIcon, TranslateIcon, SpeakIcon, MovieIcon, EyeIcon, EyeSlashIcon } from './components/icons';
import { SubtitleLine } from './types';
import { videoService } from './services/videoService';
import { apiVideoService } from './services/apiVideoService';

interface MergedSubtitleItemProps {
  subtitle: MergedSubtitle;
  onUpdate: (id: string, field: keyof MergedSubtitle, value: string) => void;
  onDelete: (id: string) => void;
  index: number;
  isReadOnly?: boolean;
  draggingInfo: string | null;
  dropTargetInfo: { targetId: string; position: 'before' | 'after' } | null;
  onDragStartItem: (e: React.DragEvent<HTMLDivElement>, id: string) => void;
  onDragEndItem: () => void;
  onDragEnterItem: (e: React.DragEvent<HTMLDivElement>, targetId: string) => void;
  onDropOnItem: (e: React.DragEvent<HTMLDivElement>, targetId: string) => void;
}

const MergedSubtitleItem: React.FC<MergedSubtitleItemProps> = ({
  subtitle, onUpdate, onDelete, index, isReadOnly = false,
  draggingInfo, dropTargetInfo,
  onDragStartItem, onDragEndItem, onDragEnterItem, onDropOnItem
}) => {
  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, field: keyof MergedSubtitle) => {
    if (!isReadOnly) {
      onUpdate(subtitle.id, field, e.target.value);
    }
  };

  const isValidTimeFormat = (time: string) => /^(\d{2}):(\d{2}):(\d{2}),(\d{3})$/.test(time);

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>) => {
    if (isReadOnly) return;
    onDragStartItem(e, subtitle.id);
  };

  const handleDragEnd = () => {
    if (isReadOnly) return;
    onDragEndItem();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    if (isReadOnly) return;
    if (draggingInfo === subtitle.id) return;
    onDragEnterItem(e, subtitle.id);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    if (isReadOnly || !draggingInfo || draggingInfo === subtitle.id ) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    if (isReadOnly) return;
    onDropOnItem(e, subtitle.id);
  };

  const isBeingDragged = draggingInfo === subtitle.id;
  const isDropTargetBefore = dropTargetInfo?.targetId === subtitle.id && dropTargetInfo?.position === 'before';
  const isDropTargetAfter = dropTargetInfo?.targetId === subtitle.id && dropTargetInfo?.position === 'after';

  return (
    <div
      id={`subtitle-item-${subtitle.id}`}
      className={`grid grid-cols-1 md:grid-cols-[auto_repeat(2,minmax(0,max-content))_repeat(2,1fr)_auto] gap-x-3 gap-y-2 items-start p-3 ${isReadOnly ? 'bg-slate-700/50' : 'bg-slate-700'} rounded-lg shadow
                 ${isBeingDragged ? 'opacity-50 ring-2 ring-sky-500 ring-offset-2 ring-offset-slate-800' : ''}
                 ${isDropTargetBefore ? 'border-t-2 border-sky-500' : ''}
                 ${isDropTargetAfter ? 'border-b-2 border-sky-500' : ''}
                 transition-opacity duration-150`}
      draggable={!isReadOnly}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div className="text-xs font-medium text-slate-400 md:text-center pt-2 pr-2 select-none">{index + 1}</div>

      <input
        type="text"
        value={subtitle.startTime}
        onChange={(e) => handleInputChange(e, 'startTime')}
        placeholder="00:00:00,000"
        className={`p-2 ${isReadOnly ? 'bg-slate-800/50 cursor-not-allowed' : 'bg-slate-800 border-slate-600 focus:border-sky-500'} ${!isReadOnly && !isValidTimeFormat(subtitle.startTime) ? 'border-red-500' : ''} rounded-md text-xs w-full`}
        aria-label="Start time"
        readOnly={isReadOnly}
      />
      <input
        type="text"
        value={subtitle.endTime}
        onChange={(e) => handleInputChange(e, 'endTime')}
        placeholder="00:00:00,000"
        className={`p-2 ${isReadOnly ? 'bg-slate-800/50 cursor-not-allowed' : 'bg-slate-800 border-slate-600 focus:border-sky-500'} ${!isReadOnly && !isValidTimeFormat(subtitle.endTime) ? 'border-red-500' : ''} rounded-md text-xs w-full`}
        aria-label="End time"
        readOnly={isReadOnly}
      />
      <textarea
        value={subtitle.originalText}
        onChange={(e) => handleInputChange(e, 'originalText')}
        rows={3}
        className={`p-2 ${isReadOnly ? 'bg-slate-800/50 cursor-not-allowed' : 'bg-slate-800 border-slate-600 focus:ring-2 focus:ring-sky-500 focus:border-sky-500'} rounded-md text-xs w-full min-h-[6em]`}
        placeholder="Original text"
        aria-label="Original subtitle text"
        readOnly={isReadOnly}
      />
      <textarea
        value={subtitle.translatedText}
        onChange={(e) => handleInputChange(e, 'translatedText')}
        rows={3}
        className={`p-2 ${isReadOnly ? 'bg-slate-800/50 cursor-not-allowed' : 'bg-slate-800 border-slate-600 focus:ring-2 focus:ring-sky-500 focus:border-sky-500'} rounded-md text-xs w-full min-h-[6em]`}
        placeholder="Translated text"
        aria-label="Translated subtitle text"
        readOnly={isReadOnly}
      />
      {!isReadOnly && (
        <button
          onClick={() => onDelete(subtitle.id)}
          className="p-2 bg-red-600 hover:bg-red-700 rounded-md text-white transition-colors flex items-center justify-center md:col-start-6"
          aria-label="Delete subtitle"
        >
          <DeleteIcon className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};


const App: React.FC = () => {
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoFileName, setVideoFileName] = useState<string>('');
  const [videoDescription, setVideoDescription] = useState<string>('');
  const [mergedSubtitles, setMergedSubtitles] = useState<MergedSubtitle[]>([]);

  const [audioExtractionLanguage, setAudioExtractionLanguage] = useState<string>('Vietnamese (vi)');
  const [targetLanguage, setTargetLanguage] = useState<string>('Thai (th)');
  const [selectedProvider, setSelectedProvider] = useState<string>('gemini');

  // API Key management with localStorage
  const [apiKeys, setApiKeys] = useState<{[key: string]: string}>(() => {
    try {
      const savedKeys = localStorage.getItem('ai-subtitle-studio-api-keys');
      return savedKeys ? JSON.parse(savedKeys) : { openai: '', gemini: '' };
    } catch (error) {
      console.warn('Failed to load API keys from localStorage:', error);
      return { openai: '', gemini: '' };
    }
  });
  const [showApiKeySettings, setShowApiKeySettings] = useState<boolean>(false);
  const [showApiKeyValues, setShowApiKeyValues] = useState<{[key: string]: boolean}>({
    openai: false,
    gemini: false
  });

  // API Key management functions
  const updateApiKey = (provider: string, key: string) => {
    const newApiKeys = { ...apiKeys, [provider]: key };
    setApiKeys(newApiKeys);

    // Save to localStorage
    try {
      localStorage.setItem('ai-subtitle-studio-api-keys', JSON.stringify(newApiKeys));
    } catch (error) {
      console.warn('Failed to save API keys to localStorage:', error);
    }
  };

  const getApiKey = (provider: string): string => {
    return apiKeys[provider] || '';
  };

  const clearApiKeys = () => {
    const emptyKeys = { openai: '', gemini: '' };
    setApiKeys(emptyKeys);
    try {
      localStorage.removeItem('ai-subtitle-studio-api-keys');
    } catch (error) {
      console.warn('Failed to clear API keys from localStorage:', error);
    }
  };

  const toggleApiKeyVisibility = (provider: string) => {
    setShowApiKeyValues(prev => ({
      ...prev,
      [provider]: !prev[provider]
    }));
  };

  const ttsServiceOptions = [
    { id: '', name: 'None' },
    { id: 'geminiConceptualTTS', name: 'Gemini TTS (Conceptual)' },
    { id: 'mockEngineAdvanced', name: 'Advanced AI Voice Engine (Mock)' },
    { id: 'mockEngineStandard', name: 'Standard Robotic Voice (Mock)' },
    { id: 'mockEngineFriendly', name: 'Friendly Natural Voice (Mock)' },
  ];
  const [selectedTtsService, setSelectedTtsService] = useState<string>('');
  const [ttsLanguageTarget, setTtsLanguageTarget] = useState<'source' | 'target'>('source');

  const [embedOriginalSpeech, setEmbedOriginalSpeech] = useState<boolean>(true); // Default to true for preview
  const [embedTranslatedSpeech, setEmbedTranslatedSpeech] = useState<boolean>(true); // Default to true for preview
  const [isLoadingOriginal, setIsLoadingOriginal] = useState<boolean>(false);
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [infoMessage, setInfoMessage] = useState<string | null>(null);

  // Video processing state
  const [isProcessingVideo, setIsProcessingVideo] = useState<boolean>(false);
  const [videoProcessingProgress, setVideoProcessingProgress] = useState<VideoProcessingProgress | null>(null);
  const [processedVideo, setProcessedVideo] = useState<ProcessedVideoResult | null>(null);
  const [subtitleStyle, setSubtitleStyle] = useState<SubtitleStyle>(getDefaultSubtitleStyle());
  const [outputFormat, setOutputFormat] = useState<'mp4' | 'webm' | 'avi'>('mp4');
  const [videoObjectURL, setVideoObjectURL] = useState<string | null>(null);
  const [useApiProcessing, setUseApiProcessing] = useState<boolean>(true); // Use API by default
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const [draggingId, setDraggingId] = useState<string | null>(null);
  const [dropTargetInfo, setDropTargetInfo] = useState<{ targetId: string; position: 'before' | 'after' } | null>(null);

  // State for VTT preview URLs
  const [originalVttUrl, setOriginalVttUrl] = useState<string | null>(null);
  const [translatedVttUrl, setTranslatedVttUrl] = useState<string | null>(null);


  useEffect(() => {
    let currentObjectUrl: string | null = null;
    if (videoFile) {
      currentObjectUrl = URL.createObjectURL(videoFile);
      setVideoObjectURL(currentObjectUrl);
    } else {
      setVideoObjectURL(null);
    }
    return () => {
      if (currentObjectUrl) {
        URL.revokeObjectURL(currentObjectUrl);
      }
    };
  }, [videoFile]);

  // Cleanup FFmpeg on unmount
  useEffect(() => {
    return () => {
      videoService.terminate();
    };
  }, []);

  const extractLangCode = (langStringWithCode: string): string => {
    const match = langStringWithCode.match(/\(([^)]+)\)$/);
    return match ? match[1] : 'und'; // Default to 'undetermined' if no code found
  };

  // Effect to generate VTT content for preview
  useEffect(() => {
    let tempOriginalVttUrl: string | null = null;
    let tempTranslatedVttUrl: string | null = null;

    if (embedOriginalSpeech && mergedSubtitles.length > 0) {
      const originalSubtitlesWithContent = mergedSubtitles.filter(s => s.originalText.trim() !== '');
      if (originalSubtitlesWithContent.length > 0) {
        const vttContent = generateVTTContent(originalSubtitlesWithContent, 'original');
        const blob = new Blob([vttContent], { type: 'text/vtt;charset=utf-8' });
        tempOriginalVttUrl = URL.createObjectURL(blob);
        setOriginalVttUrl(tempOriginalVttUrl);
      } else {
         setOriginalVttUrl(null);
      }
    } else {
      setOriginalVttUrl(null);
    }

    if (embedTranslatedSpeech && mergedSubtitles.length > 0) {
      const translatedSubtitlesWithContent = mergedSubtitles.filter(s => s.translatedText.trim() !== '');
      if (translatedSubtitlesWithContent.length > 0) {
        const vttContent = generateVTTContent(translatedSubtitlesWithContent, 'translated');
        const blob = new Blob([vttContent], { type: 'text/vtt;charset=utf-8' });
        tempTranslatedVttUrl = URL.createObjectURL(blob);
        setTranslatedVttUrl(tempTranslatedVttUrl);
      } else {
        setTranslatedVttUrl(null);
      }
    } else {
      setTranslatedVttUrl(null);
    }

    if (videoRef.current) {
        // videoRef.current.load(); // Consider if this is needed or causes issues
    }

    return () => {
      if (tempOriginalVttUrl) URL.revokeObjectURL(tempOriginalVttUrl);
      if (tempTranslatedVttUrl) URL.revokeObjectURL(tempTranslatedVttUrl);
    };
  }, [mergedSubtitles, embedOriginalSpeech, embedTranslatedSpeech, audioExtractionLanguage, targetLanguage]);


  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    setError(null);
    setInfoMessage(null);
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      if (file.type.startsWith('video/')) {
        setVideoFile(file);
        setVideoFileName(file.name);
      } else {
        setError("Invalid file type. Please upload a video file.");
        setVideoFile(null);
        setVideoFileName('');
         if(fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    }
  };

  const triggerGenerateOriginalSubtitles = async () => {
    if (!videoFile && !videoDescription.trim()) {
      setError('Please upload a video file or provide a description for transcription.');
      return;
    }
    if (!targetLanguage.trim()) {
        setError('Please specify a target language. Bilingual generation requires a target language.');
        return;
    }
    setIsLoadingOriginal(true);
    setError(null);
    setInfoMessage(null);
    setMergedSubtitles([]);
    try {
      const newMergedSubtitles: MergedSubtitle[] = await generateSubtitlesWithChatCompletion(
        selectedProvider as 'gemini' | 'openai' ,
        videoDescription,
        videoFile,
        audioExtractionLanguage,
        targetLanguage,
        getApiKey(selectedProvider)
      );
      setMergedSubtitles(newMergedSubtitles);
      setInfoMessage(`Bilingual subtitles (Original & ${targetLanguage}) generated successfully using ${selectedProvider.toUpperCase()} for ${videoFile ? videoFileName : 'description'}.`);
    } catch (err: any) {
      setError(err.message || 'An unknown error occurred during transcription processing.');
      console.error("Transcription Error:", err);
    } finally {
      setIsLoadingOriginal(false);
    }
  };

  const handleSubtitleChange = useCallback((id: string, field: keyof MergedSubtitle, value: string) => {
    setMergedSubtitles(prev => prev.map(sub => sub.id === id ? { ...sub, [field]: value } : sub));
  }, []);

  const addSubtitle = useCallback(() => {
    let newStartTime = "00:00:00,000";
    let newEndTime = "00:00:02,000";
    if (mergedSubtitles.length > 0) {
        const lastSub = mergedSubtitles[mergedSubtitles.length - 1];
        try {
            const parts = lastSub.endTime.match(/(\d{2}):(\d{2}):(\d{2}),(\d{3})/);
            if (parts) {
                let totalSeconds = parseInt(parts[1])*3600 + parseInt(parts[2])*60 + parseInt(parts[3]) + parseInt(parts[4])/1000;
                newStartTime = formatSecondsToSRTTime(totalSeconds + 0.5);
                newEndTime = formatSecondsToSRTTime(totalSeconds + 0.5 + 2);
            }
        } catch (e) { /* ignore parsing error, use default */ }
    }

    setMergedSubtitles(prev => [
      ...prev,
      { id: crypto.randomUUID(), startTime: newStartTime, endTime: newEndTime, originalText: '', translatedText: '' }
    ]);
  }, [mergedSubtitles]);

  const deleteSubtitle = useCallback((id: string) => {
    setMergedSubtitles(prev => prev.filter(sub => sub.id !== id));
  }, []);

  const processTranslation = async () => {
    const originalSubtitlesExist = mergedSubtitles.some(ms => ms.originalText.trim() !== '');
    if (mergedSubtitles.length === 0 || !originalSubtitlesExist) {
      setError("No original subtitles with content to translate. Please generate or add original subtitles first.");
      return;
    }
    if (!targetLanguage.trim()) {
      setError("Please specify a target language for translation.");
      return;
    }
    setIsTranslating(true);
    setError(null);
    setInfoMessage(null);

    try {
      const updatedSubtitles = await translateExistingSubtitles(mergedSubtitles, targetLanguage);
      setMergedSubtitles(updatedSubtitles);

      setInfoMessage(`Subtitles (re)translated to ${targetLanguage}.`);
    } catch (err: any) {
      setError(err.message || 'An unknown error occurred during translation.');
      console.error("Translation Error:", err);
    } finally {
      setIsTranslating(false);
    }
  };

  const processVideoWithSubtitles = async () => {
    if (!videoFile) {
      setError("Please upload a video file first.");
      return;
    }

    if (mergedSubtitles.length === 0) {
      setError("No subtitles available. Please generate subtitles first.");
      return;
    }

    if (!embedOriginalSpeech && !embedTranslatedSpeech) {
      setError("Please select at least one subtitle type to embed (Original or Translated).");
      return;
    }

    setIsProcessingVideo(true);
    setError(null);
    setInfoMessage(null);
    setProcessedVideo(null);
    setVideoProcessingProgress(null);
    setCurrentTaskId(null);

    try {
      // Extract language codes from language strings (e.g., "Vietnamese (vi)" -> "vi")
      const extractLanguageCode = (langString: string): string => {
        const match = langString.match(/\(([^)]+)\)$/);
        return match ? match[1] : langString;
      };

      const sourceLanguageCode = extractLanguageCode(audioExtractionLanguage);
      const targetLanguageCode = extractLanguageCode(targetLanguage);

      // Determine the actual language code for TTS based on user selection
      const getTtsLanguageCode = (): string | undefined => {
        if (!selectedTtsService) return undefined;
        return ttsLanguageTarget === 'source' ? sourceLanguageCode : targetLanguageCode;
      };

      const options: VideoProcessingOptions = {
        embedOriginalSubtitles: embedOriginalSpeech,
        embedTranslatedSubtitles: embedTranslatedSpeech,
        subtitleStyle,
        outputFormat,
        ttsService: selectedTtsService || undefined,
        ttsLanguageTarget: getTtsLanguageCode(),
        sourceLanguage: audioExtractionLanguage,
        targetLanguage: targetLanguage
      };

      let result: ProcessedVideoResult;

      if (useApiProcessing) {
        // Use API-based processing
        apiVideoService.setProgressCallback((progress: VideoProcessingProgress) => {
          setVideoProcessingProgress(progress);
        });

        result = await apiVideoService.processVideoWithSubtitles(videoFile, mergedSubtitles, options, getApiKey(selectedProvider));
      } else {
        // Use local FFmpeg processing (fallback)
        videoService.setProgressCallback((progress: VideoProcessingProgress) => {
          setVideoProcessingProgress(progress);
        });

        result = await videoService.processVideoWithSubtitles(videoFile, mergedSubtitles, options);
      }

      setProcessedVideo(result);
      setInfoMessage(`Video processed successfully! File size: ${formatFileSize(result.size)}`);

    } catch (err: any) {
      console.error("Video processing error:", err);
      const errorMessage = err.message || 'Video processing failed.';

      if (useApiProcessing && (errorMessage.includes('Failed to submit') || errorMessage.includes('localhost:8888'))) {
        setError(`${errorMessage}\n\nTip: Make sure the backend server is running on localhost:8888, or switch to local processing.`);
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsProcessingVideo(false);
      setVideoProcessingProgress(null);
      setCurrentTaskId(null);
    }
  };

  const downloadProcessedVideo = () => {
    if (processedVideo) {
      if (useApiProcessing) {
        // For API processing, use the download URL instead of blob
        const downloadUrl = apiVideoService.getLastDownloadUrl();
        if (downloadUrl) {
          apiVideoService.triggerManualDownload(downloadUrl, processedVideo.filename);
          setInfoMessage(`Download started: ${processedVideo.filename}`);
        } else {
          setError('Download URL not available. Please try processing the video again.');
        }
      } else {
        // For local processing, use the blob as usual
        downloadVideoFile(processedVideo.videoBlob, processedVideo.filename);
        setInfoMessage(`Downloaded: ${processedVideo.filename}`);
      }
    }
  };

  const handleNotImplemented = () => {
    setInfoMessage("This feature is conceptual and not yet implemented in this demo.");
    setError(null);
  };


  const handleExportSRT = (type: 'original' | 'translated' | 'bilingual') => {
    let subsToExport: SubtitleLine[] = [];

    if (type === 'bilingual') {
      // Create bilingual subtitles
      subsToExport = mergedSubtitles
        .filter(ms => ms.originalText.trim() !== '' || ms.translatedText.trim() !== '')
        .map(ms => {
          let combinedText = '';
          if (ms.originalText.trim()) {
            combinedText += ms.originalText.trim();
          }
          if (ms.translatedText.trim()) {
            if (combinedText) combinedText += '\n';
            combinedText += ms.translatedText.trim();
          }
          return {
            id: ms.id,
            startTime: ms.startTime,
            endTime: ms.endTime,
            text: combinedText,
          };
        });
    } else {
      // Original or translated only
      subsToExport = mergedSubtitles
        .map(ms => ({
          id: ms.id,
          startTime: ms.startTime,
          endTime: ms.endTime,
          text: type === 'original' ? ms.originalText : ms.translatedText,
        }))
        .filter(sub => sub.text.trim() !== '');
    }

    if (subsToExport.length === 0) {
      setError(`No ${type} subtitles with content to export.`);
      return;
    }

    try {
      const srtContent = generateSRTContent(subsToExport);
      let defaultFileName = '';

      if (type === 'bilingual') {
        const langSuffix = targetLanguage ? `_${targetLanguage.replace(/[^a-z0-9]/gi, '_').substring(0,10)}` : '';
        defaultFileName = `${videoFileName || 'video'}_bilingual${langSuffix}.srt`;
      } else {
        const langSuffix = type === 'translated' ? `_${targetLanguage.replace(/[^a-z0-9]/gi, '_').substring(0,10) || 'target'}` : '';
        defaultFileName = `${videoFileName || type}_subtitles${langSuffix}.srt`;
      }

      downloadSRTFile(srtContent, defaultFileName);
      setInfoMessage(`${type.charAt(0).toUpperCase() + type.slice(1)} subtitles exported successfully as ${defaultFileName}.`);
    } catch (err: any) {
      setError(err.message || "Error generating SRT file.");
      console.error("SRT Export Error:", err);
    }
  };

  const handleReset = () => {
    setVideoFile(null);
    setVideoFileName('');
    setVideoDescription('');
    setMergedSubtitles([]);
    setIsLoadingOriginal(false);
    setIsTranslating(false);
    setError(null);
    setAudioExtractionLanguage('Vietnamese (vi)');
    setTargetLanguage('Thai (th)');
    setSelectedProvider('openai');
    setEmbedOriginalSpeech(true); // Reset to default true for preview
    setEmbedTranslatedSpeech(true); // Reset to default true for preview
    setSelectedTtsService('');
    setTtsLanguageTarget('source');
    setDraggingId(null);
    setDropTargetInfo(null);

    // Reset video processing state
    setIsProcessingVideo(false);
    setVideoProcessingProgress(null);
    setProcessedVideo(null);
    setSubtitleStyle(getDefaultSubtitleStyle());
    setOutputFormat('mp4');
    setCurrentTaskId(null);
    // Keep useApiProcessing as is - don't reset user preference

    if(originalVttUrl) URL.revokeObjectURL(originalVttUrl);
    if(translatedVttUrl) URL.revokeObjectURL(translatedVttUrl);
    setOriginalVttUrl(null);
    setTranslatedVttUrl(null);

    setInfoMessage("Application reset.");
     if(fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const clearDraggingStates = useCallback(() => {
    setDraggingId(null);
    setDropTargetInfo(null);
  }, []);

  const handleReorderSubtitles = useCallback((draggedId: string, targetId: string | null, dropPosition: 'before' | 'after' | 'at_end') => {
    const draggedItemIndex = mergedSubtitles.findIndex(sub => sub.id === draggedId);
    if (draggedItemIndex === -1) return;

    const newSubtitlesList = [...mergedSubtitles];
    const [draggedItem] = newSubtitlesList.splice(draggedItemIndex, 1);

    if (dropPosition === 'at_end' || targetId === null) {
      newSubtitlesList.push(draggedItem);
    } else {
      const targetItemIndex = newSubtitlesList.findIndex(sub => sub.id === targetId);
      if (targetItemIndex === -1) {
        newSubtitlesList.push(draggedItem);
      } else {
        if (dropPosition === 'before') {
          newSubtitlesList.splice(targetItemIndex, 0, draggedItem);
        } else { // 'after'
          newSubtitlesList.splice(targetItemIndex + 1, 0, draggedItem);
        }
      }
    }
    setMergedSubtitles(newSubtitlesList);
  }, [mergedSubtitles]);

  const renderMergedSubtitleEditorPanel = () => (
    <div className="bg-slate-800 p-4 md:p-6 rounded-xl shadow-2xl flex flex-col flex-grow min-h-0">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-3">
        <h2 className="text-lg font-semibold text-sky-400 mb-2 sm:mb-0">Subtitles Editor</h2>
        <div className="text-xs text-slate-400">
            {videoFile ? `Source: ${videoFileName}` : (videoDescription.trim() ? `Source: Description` : "Awaiting input...")}
            {mergedSubtitles.some(s => s.translatedText.trim()) && targetLanguage ? ` | Target: ${targetLanguage}` : ""}
        </div>
      </div>

      <div
        className={`thin-scrollbar flex-1 overflow-y-auto space-y-3 pr-2 -mr-2 min-h-[300px] md:min-h-[400px] relative`}
        onDragOver={(e) => {
            if (!draggingId) return;
            e.preventDefault();
            e.dataTransfer.dropEffect = "move";
        }}
        onDrop={(e) => {
            e.preventDefault();
            const dataString = e.dataTransfer.getData("application/json");
            if (!dataString) { clearDraggingStates(); return; }
            const draggedData = JSON.parse(dataString) as { id: string };

            if (!draggedData) { clearDraggingStates(); return; }
            if (draggingId) {
                handleReorderSubtitles(draggedData.id, null, 'at_end');
            }
            clearDraggingStates();
        }}
         onDragLeave={(e) => {
            const listElement = e.currentTarget as HTMLElement;
            if (!listElement.contains(e.relatedTarget as Node)) {
                setDropTargetInfo(null);
            }
        }}
      >
        {(isLoadingOriginal || isTranslating) && (
          <div className="absolute inset-0 bg-slate-800/80 flex items-center justify-center z-10 rounded-lg">
            <ProcessIcon className="w-10 h-10 text-sky-500 animate-spin" />
            <p className="ml-2 text-sm">{isLoadingOriginal ? 'Generating originals...' : 'Translating...'}</p>
          </div>
        )}
        {!isLoadingOriginal && !isTranslating && mergedSubtitles.length === 0 && <p className="text-slate-400 text-center py-4 text-xs">No subtitles yet. Generate originals or add manually.</p>}
        {mergedSubtitles.map((sub, index) => (
          <MergedSubtitleItem
            key={sub.id}
            subtitle={sub}
            onUpdate={handleSubtitleChange}
            onDelete={deleteSubtitle}
            index={index}
            isReadOnly={isLoadingOriginal || isTranslating}
            draggingInfo={draggingId}
            dropTargetInfo={dropTargetInfo}
            onDragStartItem={(e, id) => {
              e.dataTransfer.effectAllowed = "move";
              e.dataTransfer.setData("application/json", JSON.stringify({ id }));
              setDraggingId(id);
            }}
            onDragEndItem={clearDraggingStates}
            onDragEnterItem={(e, targetId) => {
              if (!draggingId || draggingId === targetId) return;
              const targetElement = (e.target as HTMLElement).closest(`#subtitle-item-${targetId}`);
              if (targetElement) {
                const rect = targetElement.getBoundingClientRect();
                const mouseY = e.clientY;
                const midY = rect.top + rect.height / 2;
                setDropTargetInfo({ targetId, position: mouseY < midY ? 'before' : 'after' });
              }
            }}
            onDropOnItem={(e, targetId) => {
              e.preventDefault();
              e.stopPropagation();
              const dataString = e.dataTransfer.getData("application/json");
              if (!dataString) { clearDraggingStates(); return; }
              const draggedData = JSON.parse(dataString) as { id: string };

              if (!draggedData || draggedData.id === targetId) {
                clearDraggingStates(); return;
              }

              const targetElement = (e.target as HTMLElement).closest(`#subtitle-item-${targetId}`);
              let dropPosition: 'before' | 'after' = 'after';
              if (targetElement) {
                  const rect = targetElement.getBoundingClientRect();
                  const mouseY = e.clientY;
                  const midY = rect.top + rect.height / 2;
                  dropPosition = mouseY < midY ? 'before' : 'after';
              }
              handleReorderSubtitles(draggedData.id, targetId, dropPosition);
              clearDraggingStates();
            }}
          />
        ))}
      </div>
      <button
        onClick={addSubtitle}
        disabled={isLoadingOriginal || isTranslating}
        className="mt-4 w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2.5 px-4 rounded-md transition-colors flex items-center justify-center gap-2 disabled:bg-slate-600 disabled:cursor-not-allowed"
      >
        <AddIcon className="w-5 h-5" /> Add Subtitle Line
      </button>
      <div className="mt-3 grid grid-cols-1 sm:grid-cols-3 gap-3">
        <button
          onClick={() => handleExportSRT('original')}
          disabled={mergedSubtitles.filter(s => s.originalText.trim() !== '').length === 0 || isLoadingOriginal || isTranslating}
          className="w-full bg-sky-600 hover:bg-sky-700 text-white font-medium py-2.5 px-4 rounded-md transition-colors flex items-center justify-center gap-2 disabled:bg-slate-600 disabled:cursor-not-allowed"
        >
          <DownloadIcon className="w-5 h-5" /> Export Original .SRT
        </button>
        <button
          onClick={() => handleExportSRT('translated')}
          disabled={mergedSubtitles.filter(s => s.translatedText.trim() !== '').length === 0 || isLoadingOriginal || isTranslating}
          className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2.5 px-4 rounded-md transition-colors flex items-center justify-center gap-2 disabled:bg-slate-600 disabled:cursor-not-allowed"
        >
          <DownloadIcon className="w-5 h-5" /> Export Translated .SRT
        </button>
        <button
          onClick={() => handleExportSRT('bilingual')}
          disabled={mergedSubtitles.filter(s => s.originalText.trim() !== '' || s.translatedText.trim() !== '').length === 0 || isLoadingOriginal || isTranslating}
          className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2.5 px-4 rounded-md transition-colors flex items-center justify-center gap-2 disabled:bg-slate-600 disabled:cursor-not-allowed"
        >
          <DownloadIcon className="w-5 h-5" /> Export Bilingual .SRT
        </button>
      </div>
    </div>
  );

  const renderVideoPreviewPanel = () => (
    <div className="bg-slate-800 p-3 md:p-4 rounded-xl shadow-2xl aspect-video flex items-center justify-center min-h-[200px] w-full h-full">
      {videoObjectURL ? (
        <video ref={videoRef} key={videoObjectURL} src={videoObjectURL} controls className="w-full h-full rounded-lg" aria-label={videoFileName || "Video preview"} crossOrigin="anonymous">
          {originalVttUrl && (
            <track
              kind="subtitles"
              src={originalVttUrl}
              srcLang={extractLangCode(audioExtractionLanguage)}
              label={`Original (${extractLangCode(audioExtractionLanguage)})`}
              default={embedOriginalSpeech && (!embedTranslatedSpeech || !translatedVttUrl)}
            />
          )}
          {translatedVttUrl && (
            <track
              kind="subtitles"
              src={translatedVttUrl}
              srcLang={extractLangCode(targetLanguage)}
              label={`Translated (${extractLangCode(targetLanguage)})`}
              default={embedTranslatedSpeech}
            />
          )}
          Your browser does not support the video tag or VTT tracks.
        </video>
      ) : (
        <div className="text-slate-400 text-center">
          <MovieIcon className="w-16 h-16 mx-auto mb-2 text-slate-500" />
          <p className="text-xs">Upload a video to see preview</p>
        </div>
      )}
    </div>
  );

  const commonLanguageOptions = [
    { value: "Arabic (ar)", label: "Arabic" },
    { value: "Chinese (zh)", label: "Chinese" },
    { value: "English (en)", label: "English" },
    { value: "French (fr)", label: "French" },
    { value: "German (de)", label: "German" },
    { value: "Hindi (hi)", label: "Hindi" },
    { value: "Italian (it)", label: "Italian" },
    { value: "Japanese (ja)", label: "Japanese" },
    { value: "Korean (ko)", label: "Korean" },
    { value: "Portuguese (pt)", label: "Portuguese" },
    { value: "Russian (ru)", label: "Russian" },
    { value: "Spanish (es)", label: "Spanish" },
    { value: "Vietnamese (vi)", label: "Vietnamese" }
  ];

  const targetLanguageOptions = [
    { value: "Arabic (ar)", label: "Arabic" },
    { value: "Burmese (my)", label: "Burmese (Myanmar)" },
    { value: "Chinese (zh)", label: "Chinese (Simplified)" },
    { value: "Chinese (zh-TW)", label: "Chinese (Traditional)" },
    { value: "English (en)", label: "English" },
    { value: "Filipino (fil)", label: "Filipino" },
    { value: "French (fr)", label: "French" },
    { value: "German (de)", label: "German" },
    { value: "Hindi (hi)", label: "Hindi" },
    { value: "Indonesian (id)", label: "Indonesian" },
    { value: "Italian (it)", label: "Italian" },
    { value: "Japanese (ja)", label: "Japanese" },
    { value: "Javanese (jv)", label: "Javanese" },
    { value: "Khmer (km)", label: "Khmer (Cambodian)" },
    { value: "Korean (ko)", label: "Korean" },
    { value: "Lao (lo)", label: "Lao" },
    { value: "Malay (ms)", label: "Malay" },
    { value: "Portuguese (pt)", label: "Portuguese" },
    { value: "Russian (ru)", label: "Russian" },
    { value: "Spanish (es)", label: "Spanish" },
    { value: "Sundanese (su)", label: "Sundanese" },
    { value: "Tagalog (tl)", label: "Tagalog" },
    { value: "Thai (th)", label: "Thai" },
    { value: "Vietnamese (vi)", label: "Vietnamese" }
  ];

  const inputClass = "w-full p-2.5 bg-slate-700 border border-slate-600 rounded-md text-xs focus:ring-2 focus:ring-sky-500 focus:border-sky-500";
  const labelClass = "block text-xs font-medium text-slate-300 mb-1";
  const sectionTitleClass = "text-lg font-semibold mb-4 text-sky-400";
  const actionButtonClass = "w-full text-white font-semibold py-2.5 px-4 rounded-md transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed";


  return (
    <div className="min-h-screen bg-slate-900 text-slate-100 p-4 md:p-6 lg:p-8 flex flex-col">
      <header className="mb-6 md:mb-8 text-center">
        <h1 className="text-2xl md:text-3xl font-bold text-sky-500">AI Subtitle Studio</h1>
        <p className="text-slate-400 mt-1 text-xs">Generate, edit, translate, and embed subtitles into your videos with FFmpeg.</p>
      </header>

      {error && <div className="mb-4 p-3 bg-red-500/20 text-red-300 border border-red-500 rounded-md" role="alert" onClick={() => setError(null)}>{error} <span className="float-right font-bold cursor-pointer">X</span></div>}
      {infoMessage && <div className="mb-4 p-3 bg-sky-500/20 text-sky-300 border border-sky-500 rounded-md" role="status" onClick={() => setInfoMessage(null)}>{infoMessage} <span className="float-right font-bold cursor-pointer">X</span></div>}

      <div className="flex flex-col lg:flex-row lg:gap-6 mb-6 md:mb-8">
        {/* Pane 1: Video Processing & Configuration */}
        <div className="bg-slate-800 p-4 md:p-6 rounded-xl shadow-2xl w-full lg:w-5/12 mb-6 lg:mb-0">
          <h2 className="text-lg font-bold mb-6 text-center text-sky-300">Video Processing & Configuration</h2>
          <div className="mb-6">
            <h3 className={sectionTitleClass}>Video Source</h3>
            <div className="space-y-4">
              <div>
                <label htmlFor="videoFile" className={labelClass}>Upload Video</label>
                <div className="flex items-center gap-2">
                  <input
                    id="videoFile"
                    ref={fileInputRef}
                    type="file"
                    accept="video/*"
                    onChange={handleFileChange}
                    className="block w-full text-xs text-slate-400 file:mr-3 file:py-2 file:px-3 file:rounded-md file:border-0 file:text-xs file:font-semibold file:bg-sky-600 file:text-sky-50 hover:file:bg-sky-700 cursor-pointer"
                    aria-labelledby="videoFileLabel"
                  />
                </div>
                {videoFileName && <p id="videoFileLabel" className="text-xs text-slate-400 mt-1 truncate">Selected: {videoFileName}</p>}
              </div>
              <div>
                <label htmlFor="aiProvider" className={labelClass}>AI Provider</label>
                <select
                  id="aiProvider"
                  value={selectedProvider}
                  onChange={(e) => setSelectedProvider(e.target.value)}
                  className={inputClass}
                >
                  <option value="openai">OpenAI</option>
                  <option value="gemini">Gemini</option>
                </select>
                <p className="text-xs text-slate-400 mt-1">Choose the AI provider for subtitle generation</p>
              </div>
              <div>
                <div className="flex items-center justify-between">
                  <label className={labelClass}>API Keys</label>
                  <button
                    type="button"
                    onClick={() => setShowApiKeySettings(!showApiKeySettings)}
                    className="text-xs text-sky-400 hover:text-sky-300 underline"
                  >
                    {showApiKeySettings ? 'Hide' : 'Configure'}
                  </button>
                </div>
                {showApiKeySettings && (
                  <div className="mt-2 space-y-3 p-3 bg-slate-700 rounded-lg">
                    <div>
                      <label htmlFor="openaiApiKey" className="block text-xs font-medium text-slate-300 mb-1">
                        OpenAI API Key
                      </label>
                      <div className="relative">
                        <input
                          id="openaiApiKey"
                          type={showApiKeyValues.openai ? "text" : "password"}
                          value={apiKeys.openai}
                          onChange={(e) => updateApiKey('openai', e.target.value)}
                          placeholder="sk-..."
                          className={`${inputClass} pr-10`}
                        />
                        <button
                          type="button"
                          onClick={() => toggleApiKeyVisibility('openai')}
                          className="absolute inset-y-0 right-0 flex items-center pr-3 text-slate-400 hover:text-slate-300"
                        >
                          {showApiKeyValues.openai ? (
                            <EyeSlashIcon className="w-4 h-4" />
                          ) : (
                            <EyeIcon className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </div>
                    <div>
                      <label htmlFor="geminiApiKey" className="block text-xs font-medium text-slate-300 mb-1">
                        Gemini API Key
                      </label>
                      <div className="relative">
                        <input
                          id="geminiApiKey"
                          type={showApiKeyValues.gemini ? "text" : "password"}
                          value={apiKeys.gemini}
                          onChange={(e) => updateApiKey('gemini', e.target.value)}
                          placeholder="AI..."
                          className={`${inputClass} pr-10`}
                        />
                        <button
                          type="button"
                          onClick={() => toggleApiKeyVisibility('gemini')}
                          className="absolute inset-y-0 right-0 flex items-center pr-3 text-slate-400 hover:text-slate-300"
                        >
                          {showApiKeyValues.gemini ? (
                            <EyeSlashIcon className="w-4 h-4" />
                          ) : (
                            <EyeIcon className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-slate-400">
                        API keys are stored locally and sent via X-API-KEY header to the backend.
                      </p>
                      <button
                        type="button"
                        onClick={clearApiKeys}
                        className="text-xs text-red-400 hover:text-red-300 underline"
                      >
                        Clear All
                      </button>
                    </div>
                  </div>
                )}
              </div>
              <div>
                <label htmlFor="videoDescription" className={labelClass}>Video Content Description (Optional)</label>
                <textarea
                  id="videoDescription"
                  value={videoDescription}
                  onChange={(e) => setVideoDescription(e.target.value)}
                  rows={3}
                  className={inputClass}
                  placeholder="e.g., A tutorial about cooking pasta."
                />
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h3 className={sectionTitleClass}>Language Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="audioExtractionLanguage" className={labelClass}>Audio Language (for Extraction)</label>
                <select id="audioExtractionLanguage" value={audioExtractionLanguage} onChange={(e) => setAudioExtractionLanguage(e.target.value)} className={inputClass} >
                  {commonLanguageOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                </select>
              </div>
              <div>
                <label htmlFor="targetLanguage" className={labelClass}>Target Language (for Translation)</label>
                <select id="targetLanguage" value={targetLanguage} onChange={(e) => setTargetLanguage(e.target.value)} className={inputClass}>
                  <option value="">Select target language...</option>
                  {targetLanguageOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                </select>
              </div>
            </div>
          </div>

          {/* Removed "Preview Subtitles" section */}

          <div className="mb-6">
            <h3 className={sectionTitleClass}>Speech & Audio Configuration</h3>
            <div className="space-y-4">
              <div>
                <label htmlFor="ttsService" className={labelClass}>TTS Service</label>
                <select id="ttsService" value={selectedTtsService} onChange={(e) => setSelectedTtsService(e.target.value)} className={inputClass} >
                  {ttsServiceOptions.map(opt => <option key={opt.id} value={opt.id}>{opt.name}</option>)}
                </select>
              </div>
              {selectedTtsService && (
                <fieldset>
                  <legend className={labelClass}>TTS Language Target</legend>
                  <div className="mt-1 space-y-2 sm:space-y-0 sm:flex sm:gap-x-4">
                    <div className="flex items-center">
                      <input
                        id="ttsSourceLanguage"
                        name="ttsLanguageTarget"
                        type="radio"
                        value="source"
                        checked={ttsLanguageTarget === 'source'}
                        onChange={(e) => setTtsLanguageTarget(e.target.value as 'source' | 'target')}
                        className="h-4 w-4 text-sky-600 border-slate-500 focus:ring-sky-500"
                      />
                      <label htmlFor="ttsSourceLanguage" className="ml-2 text-xs text-slate-300">Source Language</label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="ttsTargetLanguage"
                        name="ttsLanguageTarget"
                        type="radio"
                        value="target"
                        checked={ttsLanguageTarget === 'target'}
                        onChange={(e) => setTtsLanguageTarget(e.target.value as 'source' | 'target')}
                        className="h-4 w-4 text-sky-600 border-slate-500 focus:ring-sky-500"
                      />
                      <label htmlFor="ttsTargetLanguage" className="ml-2 text-xs text-slate-300">Target Language</label>
                    </div>
                  </div>
                  <p className="text-xs text-slate-400 mt-1">Choose which language the TTS service should use for speech generation.</p>
                </fieldset>
              )}
              <fieldset>
                <legend className={labelClass}>Speech Sources & Preview</legend> {/* Updated legend */}
                <div className="mt-1 space-y-2 sm:space-y-0 sm:flex sm:gap-x-4">
                  <div className="flex items-center">
                    <input id="embedOriginalSpeech" name="embedOriginalSpeech" type="checkbox" checked={embedOriginalSpeech} onChange={(e) => setEmbedOriginalSpeech(e.target.checked)} className="h-4 w-4 text-sky-600 border-slate-500 rounded focus:ring-sky-500" />
                    <label htmlFor="embedOriginalSpeech" className="ml-2 text-xs text-slate-300">Original (Embed & Preview)</label> {/* Updated label */}
                  </div>
                  <div className="flex items-center">
                    <input id="embedTranslatedSpeech" name="embedTranslatedSpeech" type="checkbox" checked={embedTranslatedSpeech} onChange={(e) => setEmbedTranslatedSpeech(e.target.checked)} className="h-4 w-4 text-sky-600 border-slate-500 rounded focus:ring-sky-500" />
                    <label htmlFor="embedTranslatedSpeech" className="ml-2 text-xs text-slate-300">Translated (Embed & Preview)</label> {/* Updated label */}
                  </div>
                </div>
                <p className="text-xs text-slate-400 mt-1">Select subtitle sources to embed as speech and/or show in video preview.</p> {/* Updated helper text */}
              </fieldset>

            </div>
          </div>



          <div className="mb-6">
            <h3 className={sectionTitleClass}>Subtitle Style & Output</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="fontSize" className={labelClass}>Font Size</label>
                  <input
                    id="fontSize"
                    type="number"
                    min="12"
                    max="72"
                    value={subtitleStyle.fontSize}
                    onChange={(e) => setSubtitleStyle(prev => ({ ...prev, fontSize: Number(e.target.value) }))}
                    className={inputClass}
                  />
                </div>
                <div>
                  <label htmlFor="fontColor" className={labelClass}>Font Color</label>
                  <input
                    id="fontColor"
                    type="color"
                    value={subtitleStyle.fontColor}
                    onChange={(e) => setSubtitleStyle(prev => ({ ...prev, fontColor: e.target.value }))}
                    className="w-full h-10 rounded-md border border-slate-600 bg-slate-700"
                  />
                </div>
                <div>
                  <label htmlFor="position" className={labelClass}>Position</label>
                  <select
                    id="position"
                    value={subtitleStyle.position}
                    onChange={(e) => setSubtitleStyle(prev => ({ ...prev, position: e.target.value as 'bottom' | 'top' | 'center' }))}
                    className={inputClass}
                  >
                    <option value="bottom">Bottom</option>
                    <option value="center">Center</option>
                    <option value="top">Top</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="outputFormat" className={labelClass}>Output Format</label>
                  <select
                    id="outputFormat"
                    value={outputFormat}
                    onChange={(e) => setOutputFormat(e.target.value as 'mp4' | 'webm' | 'avi')}
                    className={inputClass}
                  >
                    <option value="mp4">MP4</option>
                    <option value="webm">WebM</option>
                    <option value="avi">AVI</option>
                  </select>
                </div>
              </div>
              <div className="flex items-center">
                <input
                  id="outline"
                  type="checkbox"
                  checked={subtitleStyle.outline}
                  onChange={(e) => setSubtitleStyle(prev => ({ ...prev, outline: e.target.checked }))}
                  className="h-4 w-4 text-sky-600 border-slate-500 rounded focus:ring-sky-500"
                />
                <label htmlFor="outline" className="ml-2 text-xs text-slate-300">Add text outline</label>
              </div>
            </div>
          </div>

          <div>
            <h3 className={sectionTitleClass}>Actions</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <button
                onClick={triggerGenerateOriginalSubtitles}
                disabled={isLoadingOriginal || isTranslating || (!videoFile && !videoDescription.trim()) || !targetLanguage.trim()}
                className={`${actionButtonClass} bg-sky-600 hover:bg-sky-700`}
                title={!targetLanguage.trim() ? "Target language is required for bilingual generation." : "Generates original and translated subtitles."}
              >
                {isLoadingOriginal ? <ProcessIcon className="w-5 h-5 animate-spin" /> : <ProcessIcon className="w-5 h-5" />}
                Generate Original Subtitles
              </button>
              <button onClick={handleNotImplemented} className={`${actionButtonClass} bg-slate-600 hover:bg-slate-500`} >
                <UploadIcon className="w-5 h-5" /> Extract Video Subtitle
              </button>
              <button
                onClick={processTranslation}
                disabled={isTranslating || isLoadingOriginal || mergedSubtitles.filter(s => s.originalText.trim() !== '').length === 0 || !targetLanguage.trim()}
                className={`${actionButtonClass} bg-indigo-600 hover:bg-indigo-700`}
                title={!targetLanguage.trim() ? "Target language is required." : (mergedSubtitles.filter(s => s.originalText.trim() !== '').length === 0 ? "No original subtitles to translate." : "Translates original subtitles to the target language.")}
              >
                {isTranslating ? <ProcessIcon className="w-5 h-5 animate-spin" /> : <TranslateIcon className="w-5 h-5" />}
                Translate with AI
              </button>
               <button onClick={processVideoWithSubtitles}
                  disabled={isProcessingVideo || isLoadingOriginal || isTranslating || !videoFile || mergedSubtitles.length === 0 || (!embedOriginalSpeech && !embedTranslatedSpeech)}
                  className={`${actionButtonClass} bg-teal-600 hover:bg-teal-700`} >
                {isProcessingVideo ? <ProcessIcon className="w-5 h-5 animate-spin" /> : <MovieIcon className="w-5 h-5" />}
                {useApiProcessing ? 'Generate Video with Subtitles (API)' : 'Generate Video with Subtitles (Local)'}
              </button>
            </div>
              {/* Video Processing Progress */}
              {(isProcessingVideo || videoProcessingProgress) && (
                <div className="mt-4 p-3 bg-slate-700/50 rounded-md">
                  <h4 className="text-xs font-medium text-sky-400 mb-2">Video Processing Progress</h4>
                  {videoProcessingProgress && (
                    <>
                      <div className="w-full bg-slate-600 rounded-full h-2 mb-2">
                        <div
                          className="bg-sky-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${videoProcessingProgress.progress}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-slate-300">{videoProcessingProgress.message}</p>
                      {videoProcessingProgress.error && (
                        <p className="text-xs text-red-400 mt-1">Error: {videoProcessingProgress.error}</p>
                      )}
                    </>
                  )}
                </div>
              )}

              {/* Processed Video Download */}
              {processedVideo && (
                <div className="mt-4 p-3 bg-green-500/20 border border-green-500 rounded-md">
                  <h4 className="text-xs font-medium text-green-400 mb-2">Video Ready for Download</h4>
                  <p className="text-xs text-green-300 mb-2">
                    {processedVideo.filename} ({formatFileSize(processedVideo.size)})
                  </p>
                  <button
                    onClick={downloadProcessedVideo}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-3 rounded-md transition-colors flex items-center justify-center gap-2"
                  >
                    <DownloadIcon className="w-4 h-4" />
                    Download Processed Video
                  </button>
                </div>
              )}

              <div className="mt-4 pt-4 border-t border-slate-700/50">
                   <h3 className="text-sm font-semibold mb-1 text-sky-400/80 flex items-center gap-1"><InfoIcon className="w-4 h-4 text-sky-400/80"/>Video Processing Info</h3>
                   <p className="text-xs text-slate-400">Video processing uses FFmpeg.wasm to embed subtitles directly into your video. The "Extract Video Subtitle" feature is still conceptual and requires additional implementation.</p>
              </div>
          </div>
        </div>

        {/* Pane 2: Video Preview */}
        <div className="w-full lg:w-7/12 flex flex-col"> {/* Ensure this pane can also grow if needed */}
          {renderVideoPreviewPanel()}
        </div>
      </div>

      <div className="flex-grow min-h-0">
        {renderMergedSubtitleEditorPanel()}
      </div>

      <footer className="mt-8 md:mt-12 text-center border-t border-slate-700 pt-6 md:pt-8">
        <button onClick={handleReset} className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors flex items-center justify-center gap-2 mx-auto" >
            <DeleteIcon className="w-5 h-5" /> Start Over / Reset All
        </button>
        <p className="text-xs text-slate-500 mt-4">AI Subtitle Studio &copy; {new Date().getFullYear()}</p>
      </footer>
    </div>
  );
};

export default App;
